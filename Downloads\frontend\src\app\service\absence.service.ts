// src/app/service/absence.service.ts
import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { Absence } from '../models/absence.model';

@Injectable({ providedIn: 'root' })
export class AbsenceService {
  private apiUrl = 'http://localhost:8089/api/absences';
  constructor(private http: HttpClient) {}
  getAll(): Observable<Absence[]>       { return this.http.get<Absence[]>(this.apiUrl); }
  getById(id: string): Observable<Absence> { return this.http.get<Absence>(`${this.apiUrl}/${id}`); }
  create(a: Absence): Observable<Absence>  { return this.http.post<Absence>(this.apiUrl, a); }
  update(id: string, a: Absence): Observable<Absence> { return this.http.put<Absence>(`${this.apiUrl}/${id}`, a); }
  delete(id: string) { return this.http.delete<void>(`${this.apiUrl}/${id}`); }
}
