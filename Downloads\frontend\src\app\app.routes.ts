import { Routes } from '@angular/router';
import { GuardService } from './service/guard.service';
import { LoginComponent } from './login/login.component';
import { SignupComponent } from './signup/signup .component';
import { UserListComponent } from './user-list/user-list.component';
import { TeamListComponent } from './team-list/team-list.component';
import { AuthGuard } from './auth.guard';
import { DashboardComponent } from './dashboard/dashboard.component';
import { AbsenceListComponent } from './absence-list/absence-list.component';


export const routes: Routes = [

  { path: 'login', component: LoginComponent },
  { path: 'signup', component: SignupComponent },

 
{ path: 'absences', component: AbsenceListComponent, canActivate: [AuthGuard] },
{ path: 'absences', loadComponent: () => import('./absence-list/absence-list.component').then(m => m.AbsenceListComponent) },

 { path: 'dashboard', component: DashboardComponent, canActivate: [AuthGuard] },
  { path: 'users', component: UserListComponent, canActivate: [AuthGuard] },
  { path: 'teams', component: TeamListComponent, canActivate: [AuthGuard] },
  { path: '**', redirectTo: 'login' }
];