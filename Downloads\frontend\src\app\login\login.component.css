@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap');

* {
  box-sizing: border-box;
  font-family: 'Inter', sans-serif;
}

.auth-background {
  height: 100vh;
  width: 100%;
  display: flex;
  justify-content: center;  /* centre horizontalement */
  align-items: center;      /* centre verticalement */
  background: linear-gradient(to right, #dbeafe, #f0f9ff);
  padding-left: 250px; /* adapte si sidebar fixe à gauche */
}

.login-container {
  width: 100%;
  max-width: 420px;
  background: white;
  padding: 2.5rem 2rem;
  border-radius: 1.2rem;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.08);
}

.login-box .title {
  text-align: center;
  margin-bottom: 2rem;
  font-size: 1.8rem;
  color: #1e293b;
  font-weight: 700;
}

.login-form {
  display: flex;
  flex-direction: column;
}

.login-form label {
  margin-bottom: 0.3rem;
  font-weight: 600;
  color: #1e293b;
}

.login-form input {
  padding: 0.7rem 0.9rem;
  margin-bottom: 1.2rem;
  border: 1px solid #cbd5e1;
  border-radius: 8px;
  font-size: 1rem;
}

.login-form input:focus {
  outline: none;
  border-color: #38bdf8;
  box-shadow: 0 0 0 3px rgba(56, 189, 248, 0.3);
}

.login-form button {
  padding: 0.8rem;
  background-color: #0ea5e9;
  color: white;
  border: none;
  font-size: 1rem;
  font-weight: 600;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.login-form button:hover {
  background-color: #0284c7;
}

.redirect-text {
  text-align: center;
  margin-top: 1.4rem;
  font-size: 0.95rem;
}

.redirect-text a {
  color: #007bff;
  text-decoration: none;
  font-weight: 600;
}

.redirect-text a:hover {
  text-decoration: underline;
}

.login-message {
  margin-top: 1rem;
  text-align: center;
  color: red;
  font-size: 0.9rem;
}
