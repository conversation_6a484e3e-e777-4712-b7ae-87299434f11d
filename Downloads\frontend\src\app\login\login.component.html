<div class="auth-container">
  <h2>Login</h2>
  <p *ngIf="message" class="message">{{ message }}</p>

  <form (ngSubmit)="handleSubmit()">
    <input
      type="text"
      placeholder="Username or Email..."
      [(ngModel)]="formData.username"
      name="username"
      required
    />
    <input
      type="password"
      placeholder="Password..."
      [(ngModel)]="formData.password"
      name="password"
      required
    />
    <button type="submit">Login</button>
  </form>

  <p>
    Don't have an account?
    <a routerLink="/register" routerLinkActive="router-link-active">Register</a>
  </p>
</div>
 
