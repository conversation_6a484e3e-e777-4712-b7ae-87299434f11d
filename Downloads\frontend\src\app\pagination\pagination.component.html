<div class="pagination-container">
    <!-- PREVIOIUS BUTTON -->
  <button
    class="pagination-button"
    [disabled]="currentPage === 1"
    (click)="onPageChange(currentPage - 1)"
  >
    &laquo; Prev
  </button>

    <!-- Page numbers BUTTONs -->
  <button
    *ngFor="let number of pageNumbers"
    class="pagination-button"
    [class.active]="currentPage === number"
    (click)="onPageChange(number)"
  >
    {{ number }}
  </button>

    <!-- NEXT BUTTON -->
  <button
    class="pagination-button"
    [disabled]="currentPage === totalPages"
    (click)="onPageChange(currentPage + 1)"
  >
    Next &raquo;
  </button>
</div>
