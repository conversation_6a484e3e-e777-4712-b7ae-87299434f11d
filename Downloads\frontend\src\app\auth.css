.auth-layout {
  display: grid;
  grid-template-columns: 1fr minmax(420px, 520px);
  min-height: calc(100vh - 0px);
}

.auth-panel {
  max-width: 520px;
  padding: 48px 40px;
  margin: 0 auto;
}

.auth-panel h1 {
  font-size: 40px;
  margin: 0 0 24px;
  font-weight: 800;
}

.field { margin-bottom: 16px; }
.field label { display:block; font-weight:600; margin-bottom:8px; }
.field input {
  width: 100%;
  height: 44px;
  padding: 0 12px;
  border: 1px solid #dfe3e8;
  border-radius: 10px;
  background: #f7f9fc;
  outline: none;
}
.field input:focus { border-color: #8ab4f8; background: #fff; }

.checkbox { display: flex; gap: 10px; align-items: center; margin: 8px 0 16px; }

button {
  width: 100%;
  height: 46px;
  border: 0;
  border-radius: 10px;
  font-weight: 700;
  font-size: 16px;
  background: #635bff;
  color: #fff;
  cursor: pointer;
  box-shadow: 0 6px 18px rgba(99,91,255,.25);
}
button[disabled] { opacity: .6; cursor: not-allowed; }

.muted { color:#6b7280; }
.small { font-size: 12px; }
.center { text-align: center; margin-top: 12px; }

.error { color:#b00020; font-size: 12px; margin-top: 6px; }
.server-error { color:#b00020; margin: 8px 0; }
.server-ok { color:#0f766e; margin: 8px 0; }

.auth-aside { display: none; }
@media (min-width: 1024px) {
  .auth-aside {
    display: block;
    background: #fafafa;
  }
  .aside-image {
    height: 100%;
    background-image: url('https://images.unsplash.com/photo-1501785888041-af3ef285b470?q=80&w=1976&auto=format&fit=crop');
    background-size: cover;
    background-position: center;
    filter: contrast(1.02) saturate(1.05);
  }
}
