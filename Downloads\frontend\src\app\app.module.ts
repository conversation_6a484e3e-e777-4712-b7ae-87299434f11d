import { NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http';

import { AppComponent } from './app.component';
import { LoginComponent } from './login/login.component';
import { SignupComponent } from './signup/signup .component';
import { TeamFormComponent } from './team-form/team-form.component';
import { TeamListComponent } from './team-list/team-list.component';
import { DashboardComponent } from './dashboard/dashboard.component';

import { NgChartsModule } from 'ng2-charts';
import { routes } from './app.routes';
import { JwtInterceptor } from './jwt.interceptor';

// Angular Material
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule }    from '@angular/material/input';
import { MatTableModule }    from '@angular/material/table';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatSortModule }     from '@angular/material/sort';
import { MatButtonModule }   from '@angular/material/button';

// ⬇️ Composant standalone
import { AbsenceListComponent } from './absence-list/absence-list.component';

@NgModule({
  declarations: [
    AppComponent,
    LoginComponent,
    SignupComponent,
    TeamFormComponent,
    TeamListComponent,
    DashboardComponent
    // ❌ NE PAS déclarer AbsenceListComponent ici
  ],
  imports: [
    BrowserModule,
    FormsModule,
    ReactiveFormsModule,
    HttpClientModule,
    RouterModule.forRoot(routes),
    NgChartsModule,

    // Material (une seule fois chacun)
    MatFormFieldModule,
    MatInputModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatButtonModule,

    // ✅ Importer le composant standalone ici
    AbsenceListComponent
  ],
  providers: [
    { provide: HTTP_INTERCEPTORS, useClass: JwtInterceptor, multi: true }
  ],
  bootstrap: [AppComponent]
})
export class AppModule {}
