<!-- src/app/absence-list/absence-list.component.html -->
<div class="toolbar">
  <mat-form-field appearance="outline">
    <mat-label>Rechercher</mat-label>
    <input matInput (keyup)="applyFilter($event)" placeholder="Nom, type, statut...">
  </mat-form-field>
</div>

<table mat-table [dataSource]="dataSource" matSort class="mat-elevation-z8">

  <ng-container matColumnDef="user">
    <th mat-header-cell *matHeaderCellDef mat-sort-header>Utilisateur</th>
    <td mat-cell *matCellDef="let a">{{ a.user?.username }}</td>
  </ng-container>

  <ng-container matColumnDef="type">
    <th mat-header-cell *matHeaderCellDef mat-sort-header>Type</th>
    <td mat-cell *matCellDef="let a">{{ a.type }}</td>
  </ng-container>

  <ng-container matColumnDef="startDate">
    <th mat-header-cell *matHeaderCellDef mat-sort-header>Début</th>
    <td mat-cell *matCellDef="let a">{{ a.startDate | date:'mediumDate' }}</td>
  </ng-container>

  <ng-container matColumnDef="endDate">
    <th mat-header-cell *matHeaderCellDef mat-sort-header>Fin</th>
    <td mat-cell *matCellDef="let a">{{ a.endDate   | date:'mediumDate' }}</td>
  </ng-container>

  <ng-container matColumnDef="daysCount">
    <th mat-header-cell *matHeaderCellDef mat-sort-header>Jours</th>
    <td mat-cell *matCellDef="let a">{{ a.daysCount }}</td>
  </ng-container>

  <ng-container matColumnDef="status">
    <th mat-header-cell *matHeaderCellDef mat-sort-header>Statut</th>
    <td mat-cell *matCellDef="let a">{{ a.status }}</td>
  </ng-container>

  <ng-container matColumnDef="approvedBy">
    <th mat-header-cell *matHeaderCellDef>Validée par</th>
    <td mat-cell *matCellDef="let a">{{ a.approvedBy?.username }}</td>
  </ng-container>

  <ng-container matColumnDef="approvedDate">
    <th mat-header-cell *matHeaderCellDef>Validation</th>
    <td mat-cell *matCellDef="let a">{{ a.approvedDate | date:'short' }}</td>
  </ng-container>

  <ng-container matColumnDef="approvalComments">
    <th mat-header-cell *matHeaderCellDef>Commentaires</th>
    <td mat-cell *matCellDef="let a">{{ a.approvalComments }}</td>
  </ng-container>

  <ng-container matColumnDef="rejectionReason">
    <th mat-header-cell *matHeaderCellDef>Rejet</th>
    <td mat-cell *matCellDef="let a">{{ a.rejectionReason }}</td>
  </ng-container>

  <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
  <tr mat-row        *matRowDef="let row; columns: displayedColumns;"></tr>
</table>

<mat-paginator [pageSizeOptions]="[5,10,25]" showFirstLastButtons></mat-paginator>
