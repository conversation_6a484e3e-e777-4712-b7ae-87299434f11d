// absence-list.component.ts
import { CommonModule } from '@angular/common';
import { Component, OnInit, ViewChild, AfterViewInit } from '@angular/core';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { AbsenceService } from '../service/absence.service';
import { Absence } from '../models/absence.model';

@Component({
  selector: 'app-absence-list',
  standalone: true,
  templateUrl: './absence-list.component.html',
  styleUrls: ['./absence-list.component.css'],
  imports: [
    CommonModule,
    MatFormFieldModule, MatInputModule,
    MatTableModule, MatPaginatorModule, MatSortModule, MatButtonModule
  ]
})
export class AbsenceListComponent implements OnInit, AfterViewInit {
  displayedColumns = ['user','type','startDate','endDate','daysCount','status'];
  dataSource = new MatTableDataSource<Absence>([]);
  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;

  constructor(private service: AbsenceService) {}
  ngOnInit() { this.service.getAll().subscribe(d => this.dataSource.data = d); }
  ngAfterViewInit() { this.dataSource.paginator = this.paginator; this.dataSource.sort = this.sort; }
  applyFilter(e: Event) { this.dataSource.filter = (e.target as HTMLInputElement).value.trim().toLowerCase(); }
}
