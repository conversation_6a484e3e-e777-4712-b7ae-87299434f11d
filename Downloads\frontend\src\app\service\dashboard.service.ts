import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';

@Injectable({ providedIn: 'root' })
export class DashboardService {
  private baseUrl = 'http://localhost:8089/api/dashboard';

  constructor(private http: HttpClient) {}

  getDashboardStats(): Observable<any> {
    return this.http.get<any>(this.baseUrl);
  }
getDashboardStatsByDate(year: number, month: number) {
  return this.http.get<any>(`${this.baseUrl}/stats?year=${year}&month=${month}`);
}



}
