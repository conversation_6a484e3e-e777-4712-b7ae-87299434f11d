<div class="signup-wrapper">
  <div class="signup-box">
    <h1 class="title">Create an Account</h1>

    <form (ngSubmit)="onRegister()" class="signup-form">
      <div class="input-icon">
        <i class="fa fa-user"></i>
        <input
          type="text"
          name="username"
          placeholder="Username"
          [(ngModel)]="username"
          required
        />
      </div>

      <div class="input-icon">
        <i class="fa fa-envelope"></i>
        <input
          type="email"
          name="email"
          placeholder="Email"
          [(ngModel)]="email"
          required
        />
      </div>

      <div class="input-icon">
        <i class="fa fa-lock"></i>
        <input
          type="password"
          name="password"
          placeholder="Password"
          [(ngModel)]="password"
          required
        />
      </div>

      <button type="submit">Register</button>
    </form>

    <p class="redirect-text">
      Already have an account?
      <a routerLink="/login">Login</a>
    </p>
  </div>
</div>
