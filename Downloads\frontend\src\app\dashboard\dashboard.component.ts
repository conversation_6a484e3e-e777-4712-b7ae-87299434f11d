import { Component, OnInit } from '@angular/core';
import { ChartData, ChartOptions } from 'chart.js';
import { DashboardService } from '../service/dashboard.service';

@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.css']
})
export class DashboardComponent implements OnInit {

  stats: any = {};

  months: number[] = [];
  years: number[] = [];
  selectedMonth: number | null = null;
  selectedYear: number | null = null;

  barChartData: ChartData<'bar'> = {
    labels: ['Users', 'Teams', 'Projects', 'Activities', 'Absences', 'Requests', 'Hierarchies'],
    datasets: [{ 
      data: [], 
      label: 'Counts', 
      backgroundColor: [
        '#36A2EB', '#FF6384', '#FFCE56', 
        '#4CAF50', '#9C27B0', '#FF9800', '#00BCD4'
      ]
    }]
  };

  barChartOptions: ChartOptions<'bar'> = { responsive: true };

  pieChartData: ChartData<'pie'> = {
    labels: ['Users', 'Teams', 'Projects', 'Activities', 'Absences', 'Requests', 'Hierarchies'],
    datasets: [{ 
      data: [], 
      backgroundColor: [
        '#36A2EB', '#FF6384', '#FFCE56', 
        '#4CAF50', '#9C27B0', '#FF9800', '#00BCD4'
      ]
    }]
  };

  pieChartOptions: ChartOptions<'pie'> = { responsive: true };

  constructor(private dashboardService: DashboardService) {}

  ngOnInit(): void {
    // Initialiser mois et années
    this.months = Array.from({ length: 12 }, (_, i) => i + 1); // 1 à 12
    const currentYear = new Date().getFullYear();
    this.years = Array.from({ length: 5 }, (_, i) => currentYear - i).reverse();

    // Sélection par défaut : mois et année actuels
    this.selectedMonth = new Date().getMonth() + 1;
    this.selectedYear = currentYear;

    this.loadDashboardData();
  }

  loadDashboardData(): void {
    if (!this.selectedYear || !this.selectedMonth) return;

    this.dashboardService.getDashboardStatsByDate(this.selectedYear, this.selectedMonth)
      .subscribe((data) => {
        this.stats = data;

        const values = [
          data.users || 0,
          data.teams || 0,
          data.projects || 0,
          data.activities || 0,
          data.absences || 0,
          data.requests || 0,
          data.hierarchies || 0
        ];

        this.barChartData.datasets[0].data = values;
        this.pieChartData.datasets[0].data = values;
      });
  }

}
