import { Component } from '@angular/core';
import { Router } from '@angular/router';
import { AuthService } from '../service/auth.service';

@Component({
  selector: 'app-login',
  templateUrl: './login.component.html'
})
export class LoginComponent {
  formData = { username: '', password: '' }; // ✅ Initialisation obligatoire
message: any;

  constructor(private authService: AuthService, private router: Router) {}

  handleSubmit() {
    this.authService.login(
  this.formData.username,
  this.formData.password,
  this.formData
).subscribe({
      next: (res) => {
        if (res.token) {
          this.authService.saveToken(res.token);
          localStorage.setItem('username', this.formData.username); // Pour dashboard
        }
        this.router.navigate(['/dashboard']);
      },
      error: () => {
        alert('Invalid credentials');
      }
    });
  }
}
