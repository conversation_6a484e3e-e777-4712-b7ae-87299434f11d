@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css');
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap');

* {
  box-sizing: border-box;
  font-family: 'Inter', sans-serif;
}

.signup-wrapper {
  height: 100vh;
  display: flex;
  justify-content: center;      /* centrage horizontal */
  align-items: center;          /* centrage vertical */
  background: linear-gradient(to right, #e0f2fe, #f8fafc);
  padding-left: 250px;          /* ajuste si sidebar fixe à gauche */
}

.signup-box {
  background-color: #fff;
  padding: 2.5rem 2rem;
  border-radius: 1rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 420px;
}

.title {
  text-align: center;
  margin-bottom: 2rem;
  font-size: 1.8rem;
  font-weight: 700;
  color: #1e293b;
}

.signup-form {
  display: flex;
  flex-direction: column;
}

.input-icon {
  position: relative;
  margin-bottom: 1.6rem; /* ⬅️ augmente ici pour plus d’espace */
}


.input-icon i {
  position: absolute;
  top: 50%;
  left: 12px;
  transform: translateY(-50%);
  color: #94a3b8;
}

.input-icon input {
  padding-left: 2.5rem;
  padding: 0.75rem;
  font-size: 1rem;
  border-radius: 8px;
  border: 1px solid #cbd5e1;
  width: 100%;
}

.input-icon input:focus {
  border-color: #0ea5e9;
  outline: none;
  box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.3);
}

button {
  padding: 0.9rem;
  background-color: #0ea5e9;
  color: #fff;
  font-size: 1rem;
  font-weight: 600;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

button:hover {
  background-color: #0284c7;
}

.redirect-text {
  text-align: center;
  margin-top: 1.4rem;
  font-size: 0.95rem;
}

.redirect-text a {
  color: #007bff;
  text-decoration: none;
  font-weight: 600;
}

.redirect-text a:hover {
  text-decoration: underline;
}

@media screen and (max-width: 768px) {
  .signup-wrapper {
    padding-left: 0;
  }

  .signup-box {
    margin: 0 1rem;
  }
}
