<div class="dashboard-container">
  <h2>Transaction Dashboard</h2>

  <!-- 🔹 Filtres -->
  <div class="filters">
    <label>Select Month:</label>
    <select [(ngModel)]="selectedMonth" (change)="loadDashboardData()">
      <option *ngFor="let m of months" [value]="m">{{ m }}</option>
    </select>

    <label>Select Year:</label>
    <select [(ngModel)]="selectedYear" (change)="loadDashboardData()">
      <option *ngFor="let y of years" [value]="y">{{ y }}</option>
    </select>

    <button class="btn-show" (click)="loadDashboardData()">Show Monthly Data</button>
  </div>

  <!-- 🔹 Graphiques -->
  <div class="charts">
    <!-- Bar Chart -->
    <div class="chart-box">
      <canvas baseChart [data]="barChartData" [options]="barChartOptions" [type]="'bar'"></canvas>
    </div>

    <!-- Pie Chart -->
    <div class="chart-box">
      <canvas baseChart [data]="pieChartData" [options]="pieChartOptions" [type]="'pie'"></canvas>
    </div>
  </div>

  <!-- 🔹 Statistiques -->
  <div class="stats">
    <div>👤 Users: {{ stats.users || 0 }}</div>
    <div>👥 Teams: {{ stats.teams || 0 }}</div>
    <div>📁 Projects: {{ stats.projects || 0 }}</div>
    <div>⚡ Activities: {{ stats.activities || 0 }}</div>
    <div>🏖️ Absences: {{ stats.absences || 0 }}</div>
    <div>📨 Requests: {{ stats.requests || 0 }}</div>
    <div>🏛 Hierarchies: {{ stats.hierarchies || 0 }}</div>
  </div>
</div>
