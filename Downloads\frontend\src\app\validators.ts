// src/app/shared/validators.ts
import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';

export function match(controlName: string, confirmName: string): ValidatorFn {
  return (group: AbstractControl): ValidationErrors | null => {
    const control = group.get(controlName);
    const confirm = group.get(confirmName);
    if (!control || !confirm) return null;
    const same = control.value === confirm.value;
    confirm.setErrors(same ? null : { mismatch: true });
    return same ? null : { mismatch: true };
  };
}
