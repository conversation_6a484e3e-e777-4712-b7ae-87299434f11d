import { User } from './user.model';

export interface Absence {
  id?: string;
  user?: User;  // Référence à l'utilisateur
  type: 'ANNUAL_LEAVE' | 'SICK_LEAVE' | 'UNPAID_LEAVE' | 'MATERNITY_LEAVE' | 'OTHER';
  startDate: string;  // format ISO string pour LocalDate
  endDate: string;
  status?: 'PENDING' | 'APPROVED' | 'REJECTED' | 'CANCELLED';
  reason?: string;
  description?: string;
  approvedBy?: User;
  approvedDate?: string;  // ISO string pour LocalDateTime
  approvalComments?: string;
  rejectionReason?: string;
  daysCount?: number;
}
