// src/app/service/auth.service.ts
import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';

const API_URL = 'http://localhost:8089/api/auth';

@Injectable({ providedIn: 'root' })
export class AuthService {
  constructor(private http: HttpClient) {}

  signup(data: { username: string; email: string; password: string }): Observable<any> {
    return this.http.post(`${API_URL}/signup`, data); // (si backend renvoie JSON)
    // return this.http.post(`${API_URL}/signup`, data, { responseType: 'text' }); // si texte
  }

  // ✅ 2 paramètres seulement
  login(username: string, password: string, formData: { username: string; password: string; }): Observable<any> {
    return this.http.post(`${API_URL}/login`, { username, password });
  }

  saveToken(token: string) { localStorage.setItem('token', token); }
  getToken(): string | null { return localStorage.getItem('token'); }
  logout() { localStorage.removeItem('token'); }
}
