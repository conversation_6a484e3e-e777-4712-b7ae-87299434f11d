import { Component } from '@angular/core';
import { TeamService } from '../service/team.service';
import { Team } from '../models/team.model';

@Component({
  selector: 'app-team-form',
  templateUrl: './team-form.component.html'
})
export class TeamFormComponent {
  team: Team = { name: '' };

  constructor(private teamService: TeamService) {}

  createTeam() {
    this.teamService.create(this.team).subscribe(() => {
      alert('Équipe créée avec succès !');
      this.team = { name: '' }; // reset form
    });
  }
}
