<h2>Liste des équipes</h2>

<table>
  <thead>
    <tr>
      <th>Nom de l'équipe</th>
      <th>Membres</th>
      <th>Action</th>
    </tr>
  </thead>
  <tbody>
    <tr *ngFor="let team of teams">
      <td>{{ team.name }}</td>
      <td>
        <span *ngFor="let member of team.members || []; let i = index">
          {{ member.username }}<span *ngIf="i < (team.members?.length || 0) - 1">, </span>
        </span>
      </td>
      <td>
        <button (click)="deleteTeam(team.id!)">🗑️</button>
      </td>
    </tr>
  </tbody>
</table>
