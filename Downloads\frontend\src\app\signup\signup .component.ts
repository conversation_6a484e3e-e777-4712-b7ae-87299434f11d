import { Component, OnInit } from '@angular/core';
import { Form<PERSON>uilder, Validators, FormGroup } from '@angular/forms';
import { AuthService } from '../service/auth.service';
import { match } from '../validators';

@Component({
  selector: 'app-signup',
templateUrl: './signup.component.html',
  styleUrls: ['../auth.css']
})
export class SignupComponent implements OnInit {
  loading = false;
  serverError = '';
  serverOk = '';

  form!: FormGroup;

  constructor(private fb: FormBuilder, private auth: AuthService) {}

  ngOnInit(): void {
    this.form = this.fb.group({
      username: ['', [Validators.required, Validators.minLength(3)]],
      email: ['', [Validators.required, Validators.email]],
      password: ['', [
        Validators.required,
        Validators.minLength(8),
        Validators.pattern(/^(?=.*[A-Z])(?=.*[a-z])(?=.*\d).{8,}$/)
      ]],
      confirm: ['', [Validators.required]],
      terms: [false, [Validators.requiredTrue]]
    }, { validators: match('password', 'confirm') });
  }

  get f() { return this.form.controls; }

  submit(): void {
    this.serverError = '';
    this.serverOk = '';
    if (this.form.invalid) return;

    this.loading = true;
    this.auth.signup({
      username: this.f['username'].value,
      email: this.f['email'].value,
      password: this.f['password'].value
    }).subscribe({
      next: (res: any) => {
        this.serverOk = res?.message || 'User registered successfully';
        this.loading = false;
        this.form.reset({ terms: false });
      },
      error: (err) => {
        this.serverError = err?.error?.error || 'Registration failed';
        this.loading = false;
      }
    });
  }
}
