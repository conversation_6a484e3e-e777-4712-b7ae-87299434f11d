import { Component, OnInit } from '@angular/core';
import { TeamService } from '../service/team.service';
import { Team } from '../models/team.model';

@Component({
  selector: 'app-team-list',
  templateUrl: './team-list.component.html'
})
export class TeamListComponent implements OnInit {
  teams: Team[] = [];

  constructor(private teamService: TeamService) {}

  ngOnInit(): void {
    this.loadTeams();
  }

  loadTeams() {
    this.teamService.getAll().subscribe(data => this.teams = data);
  }

  deleteTeam(id: string) {
    if (confirm('Supprimer cette équipe ?')) {
      this.teamService.delete(id).subscribe(() => this.loadTeams());
    }
  }
}
